<template>
  <button 
    class="generate-button" 
    @click="handleClick" 
    :disabled="disabled || loading"
    :class="{ 'is-loading': loading }"
  >
    <el-icon v-if="!loading && icon" class="button-icon">
      <component :is="icon" />
    </el-icon>
    <el-icon v-if="loading" class="loading-icon button-icon">
      <Loading />
    </el-icon>
    <span class="button-text">{{ loading ? loadingText : text }}</span>
    <span v-if="credits" class="credits-info">{{ credits }}</span>
  </button>
</template>

<script setup>
import { Loading } from '@element-plus/icons-vue'

defineProps({
  text: {
    type: String,
    default: '立即生成'
  },
  loadingText: {
    type: String,
    default: '生成中...'
  },
  credits: {
    type: String,
    default: ''
  },
  icon: {
    type: [String, Object],
    default: null
  },
  loading: {
    type: Boolean,
    default: false
  },
  disabled: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['click'])

const handleClick = () => {
  emit('click')
}
</script>

<style scoped>
.generate-button {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #ffffff;
  border: none;
  padding: 10px 20px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  gap: 8px;
  width: 100%;
  justify-content: center;
}

.generate-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.generate-button:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.generate-button:active:not(:disabled) {
  transform: translateY(0);
  box-shadow: 0 2px 8px rgba(0, 212, 255, 0.3);
}

.generate-button:disabled,
.generate-button.is-loading {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.button-icon {
  font-size: 16px;
}

.loading-icon {
  animation: rotate 1s linear infinite;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.button-text {
  font-weight: 600;
  letter-spacing: 0.5px;
}

.credits-info {
  font-size: 12px;
  opacity: 0.9;
  margin-left: 6px;
  background: rgba(255, 255, 255, 0.1);
  padding: 2px 6px;
  border-radius: 10px;
  font-weight: 500;
}

/* 暗色主题支持 */
/* body.dark .generate-button {
  background: linear-gradient(135deg, #00a3cc 0%, #007399 100%);
  box-shadow: 0 4px 12px rgba(0, 163, 204, 0.25);
}

body.dark .generate-button:hover:not(:disabled) {
  background: linear-gradient(135deg, #00b8e6 0%, #0085b3 100%);
  box-shadow: 0 6px 20px rgba(0, 184, 230, 0.4);
} */
</style>
